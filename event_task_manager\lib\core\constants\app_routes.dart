/// Rotas do aplicativo
class AppRoutes {
  // Rota inicial
  static const String initial = '/';
  
  // Autenticação
  static const String login = '/login';
  static const String register = '/register';
  
  // Home
  static const String home = '/home';
  
  // Eventos
  static const String createEvent = '/create-event';
  static const String joinEvent = '/join-event';
  static const String eventDetail = '/event/:eventId';
  static const String eventTasks = '/event/:eventId/tasks';
  static const String manageVolunteers = '/event/:eventId/volunteers';
  static const String trackTasks = '/event/:eventId/track';
  
  // Perfil
  static const String profile = '/profile';
  
  // Utilitários para construir rotas com parâmetros
  static String eventDetailPath(String eventId) => '/event/$eventId';
  static String eventTasksPath(String eventId) => '/event/$eventId/tasks';
  static String manageVolunteersPath(String eventId) => '/event/$eventId/volunteers';
  static String trackTasksPath(String eventId) => '/event/$eventId/track';
}
