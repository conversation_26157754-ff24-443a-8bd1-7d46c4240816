import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

/// Funções auxiliares do aplicativo
class Helpers {
  static const _uuid = Uuid();

  /// Gera um UUID único
  static String generateId() => _uuid.v4();

  /// Gera uma TAG única para eventos (6 caracteres alfanuméricos)
  static String generateEventTag() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var tag = '';
    
    for (int i = 0; i < 6; i++) {
      tag += chars[(random + i) % chars.length];
    }
    
    return tag;
  }

  /// Formata data para exibição
  static String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  /// Formata data e hora para exibição
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  /// Formata apenas a hora
  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  /// Formata duração em minutos para texto legível
  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}min';
    }
    
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    
    if (remainingMinutes == 0) {
      return '${hours}h';
    }
    
    return '${hours}h ${remainingMinutes}min';
  }

  /// Converte string de tempo (HH:mm) para DateTime
  static DateTime? parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      if (hour < 0 || hour > 23 || minute < 0 || minute > 59) return null;
      
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, hour, minute);
    } catch (e) {
      return null;
    }
  }

  /// Calcula a diferença em dias entre duas datas
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }

  /// Verifica se uma data está no passado
  static bool isInPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Verifica se uma data é hoje
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// Capitaliza a primeira letra de uma string
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Trunca texto com reticências
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// Remove acentos de uma string
  static String removeAccents(String text) {
    const withAccents = 'àáâãäåæçèéêëìíîïñòóôõöøùúûüýÿ';
    const withoutAccents = 'aaaaaaeceeeeiiiinoooooouuuuyy';
    
    String result = text.toLowerCase();
    for (int i = 0; i < withAccents.length; i++) {
      result = result.replaceAll(withAccents[i], withoutAccents[i]);
    }
    return result;
  }

  /// Normaliza string para busca (remove acentos, espaços extras, etc.)
  static String normalizeForSearch(String text) {
    return removeAccents(text.trim().toLowerCase().replaceAll(RegExp(r'\s+'), ' '));
  }

  /// Calcula porcentagem
  static double calculatePercentage(int part, int total) {
    if (total == 0) return 0.0;
    return (part / total) * 100;
  }

  /// Formata porcentagem para exibição
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Converte lista de strings em string separada por vírgulas
  static String joinList(List<String> list) {
    return list.join(', ');
  }

  /// Converte string separada por vírgulas em lista
  static List<String> splitString(String text) {
    return text.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }

  /// Valida se uma string é um email válido
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Gera cor baseada em string (para avatars, etc.)
  static int generateColorFromString(String text) {
    int hash = 0;
    for (int i = 0; i < text.length; i++) {
      hash = text.codeUnitAt(i) + ((hash << 5) - hash);
    }
    return hash;
  }

  /// Debounce para evitar múltiplas chamadas rápidas
  static void debounce(Function() action, {Duration delay = const Duration(milliseconds: 500)}) {
    Timer? timer;
    timer?.cancel();
    timer = Timer(delay, action);
  }
}

/// Timer para debounce
class Timer {
  static Timer? _timer;
  
  Timer(Duration duration, Function() callback) {
    _timer?.cancel();
    _timer = Timer._(duration, callback);
  }
  
  Timer._(Duration duration, Function() callback) {
    Future.delayed(duration, callback);
  }
  
  void cancel() {
    _timer = null;
  }
}
