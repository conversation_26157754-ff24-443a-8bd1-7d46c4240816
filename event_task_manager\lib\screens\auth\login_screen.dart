import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/extensions.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_widget.dart';

/// Tela de login
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  void initState() {
    super.initState();
    // Limpar erros ao entrar na tela
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().clearError();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo e título
                _buildHeader(),
                
                const SizedBox(height: 48),
                
                // Botão de login
                _buildLoginButton(),
                
                const SizedBox(height: 24),
                
                // Mensagem de erro
                _buildErrorMessage(),
                
                const SizedBox(height: 48),
                
                // Informações sobre o app
                _buildAppInfo(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Constrói o cabeçalho com logo e título
  Widget _buildHeader() {
    return Column(
      children: [
        // Ícone do app
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.event_note,
            size: 64,
            color: AppColors.onPrimary,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Título
        Text(
          AppStrings.appName,
          style: context.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onBackground,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        // Subtítulo
        Text(
          AppStrings.appDescription,
          style: context.textTheme.bodyLarge?.copyWith(
            color: AppColors.onBackground.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Constrói o botão de login
  Widget _buildLoginButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isLoading) {
          return const LoadingWidget();
        }

        return CustomButton(
          text: AppStrings.loginWithGoogle,
          onPressed: _handleGoogleSignIn,
          icon: Icons.login,
          backgroundColor: AppColors.primary,
          textColor: AppColors.onPrimary,
        );
      },
    );
  }

  /// Constrói a mensagem de erro
  Widget _buildErrorMessage() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.errorMessage == null) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.errorContainer,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.error.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: AppColors.onErrorContainer,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  authProvider.errorMessage!,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.onErrorContainer,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => authProvider.clearError(),
                icon: Icon(
                  Icons.close,
                  color: AppColors.onErrorContainer,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Constrói as informações sobre o app
  Widget _buildAppInfo() {
    return Column(
      children: [
        Text(
          'Gerencie tarefas em eventos de forma colaborativa',
          style: context.textTheme.bodyMedium?.copyWith(
            color: AppColors.onBackground.withOpacity(0.6),
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 16),
        
        // Features
        _buildFeatureList(),
      ],
    );
  }

  /// Constrói a lista de funcionalidades
  Widget _buildFeatureList() {
    final features = [
      'Crie e gerencie eventos',
      'Distribua tarefas para voluntários',
      'Acompanhe o progresso em tempo real',
      'Sistema de atribuição inteligente',
    ];

    return Column(
      children: features.map((feature) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 16,
              color: AppColors.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                feature,
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.onBackground.withOpacity(0.7),
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  /// Manipula o login com Google
  Future<void> _handleGoogleSignIn() async {
    final authProvider = context.read<AuthProvider>();
    
    final success = await authProvider.signInWithGoogle();
    
    if (success && mounted) {
      // Navegar para a tela principal
      // A navegação será tratada pelo router baseado no estado de autenticação
    }
  }
}
