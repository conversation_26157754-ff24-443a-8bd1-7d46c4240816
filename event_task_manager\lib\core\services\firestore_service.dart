import 'package:cloud_firestore/cloud_firestore.dart';
import '../errors/exceptions.dart';
import '../../models/user_model.dart';
import '../../models/event_model.dart';
import '../../models/task_model.dart';
import '../../models/microtask_model.dart';

/// Serviço para operações no Firestore
class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _usersCollection = 'users';
  static const String _eventsCollection = 'events';
  static const String _tasksCollection = 'tasks';
  static const String _microtasksCollection = 'microtasks';

  /// CRUD para Usuários

  /// Cria ou atualiza um usuário
  Future<void> createOrUpdateUser(UserModel user) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(user.id)
          .set(user.toMap(), SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao salvar usuário: ${e.toString()}');
    }
  }

  /// Obtém um usuário por ID
  Future<UserModel?> getUser(String userId) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      return UserModel.fromMap(doc.data()!);
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar usuário: ${e.toString()}');
    }
  }

  /// Deleta um usuário
  Future<void> deleteUser(String userId) async {
    try {
      await _firestore.collection(_usersCollection).doc(userId).delete();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao deletar usuário: ${e.toString()}');
    }
  }

  /// CRUD para Eventos

  /// Cria um evento
  Future<void> createEvent(EventModel event) async {
    try {
      await _firestore
          .collection(_eventsCollection)
          .doc(event.id)
          .set(event.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao criar evento: ${e.toString()}');
    }
  }

  /// Atualiza um evento
  Future<void> updateEvent(EventModel event) async {
    try {
      await _firestore
          .collection(_eventsCollection)
          .doc(event.id)
          .update(event.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao atualizar evento: ${e.toString()}');
    }
  }

  /// Obtém um evento por ID
  Future<EventModel?> getEvent(String eventId) async {
    try {
      final doc = await _firestore
          .collection(_eventsCollection)
          .doc(eventId)
          .get();

      if (!doc.exists) return null;

      return EventModel.fromMap(doc.data()!);
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar evento: ${e.toString()}');
    }
  }

  /// Obtém evento por TAG
  Future<EventModel?> getEventByTag(String eventTag) async {
    try {
      final query = await _firestore
          .collection(_eventsCollection)
          .where('eventTag', isEqualTo: eventTag.toUpperCase())
          .limit(1)
          .get();

      if (query.docs.isEmpty) return null;

      return EventModel.fromMap(query.docs.first.data());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar evento por TAG: ${e.toString()}');
    }
  }

  /// Obtém eventos do usuário (criados ou participando)
  Future<List<EventModel>> getUserEvents(String userId) async {
    try {
      // Eventos criados pelo usuário
      final createdQuery = await _firestore
          .collection(_eventsCollection)
          .where('createdBy', isEqualTo: userId)
          .get();

      // Eventos onde é gerenciador
      final managedQuery = await _firestore
          .collection(_eventsCollection)
          .where('managers', arrayContains: userId)
          .get();

      // Eventos onde é voluntário
      final volunteerQuery = await _firestore
          .collection(_eventsCollection)
          .where('volunteers', arrayContains: {'userId': userId})
          .get();

      final Set<EventModel> events = {};

      // Adicionar eventos criados
      for (final doc in createdQuery.docs) {
        events.add(EventModel.fromMap(doc.data()));
      }

      // Adicionar eventos gerenciados
      for (final doc in managedQuery.docs) {
        events.add(EventModel.fromMap(doc.data()));
      }

      // Adicionar eventos como voluntário
      for (final doc in volunteerQuery.docs) {
        final event = EventModel.fromMap(doc.data());
        if (event.volunteers.any((v) => v.userId == userId)) {
          events.add(event);
        }
      }

      return events.toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar eventos do usuário: ${e.toString()}');
    }
  }

  /// Stream de eventos do usuário
  Stream<List<EventModel>> getUserEventsStream(String userId) {
    return _firestore
        .collection(_eventsCollection)
        .where('managers', arrayContains: userId)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => EventModel.fromMap(doc.data()))
              .toList(),
        );
  }

  /// Deleta um evento
  Future<void> deleteEvent(String eventId) async {
    try {
      final batch = _firestore.batch();

      // Deletar evento
      batch.delete(_firestore.collection(_eventsCollection).doc(eventId));

      // Deletar tarefas relacionadas
      final tasks = await _firestore
          .collection(_tasksCollection)
          .where('eventId', isEqualTo: eventId)
          .get();

      for (final task in tasks.docs) {
        batch.delete(task.reference);
      }

      // Deletar microtarefas relacionadas
      final microtasks = await _firestore
          .collection(_microtasksCollection)
          .where('eventId', isEqualTo: eventId)
          .get();

      for (final microtask in microtasks.docs) {
        batch.delete(microtask.reference);
      }

      await batch.commit();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao deletar evento: ${e.toString()}');
    }
  }

  /// CRUD para Tarefas

  /// Cria uma tarefa
  Future<void> createTask(TaskModel task) async {
    try {
      await _firestore
          .collection(_tasksCollection)
          .doc(task.id)
          .set(task.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao criar tarefa: ${e.toString()}');
    }
  }

  /// Atualiza uma tarefa
  Future<void> updateTask(TaskModel task) async {
    try {
      await _firestore
          .collection(_tasksCollection)
          .doc(task.id)
          .update(task.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao atualizar tarefa: ${e.toString()}');
    }
  }

  /// Obtém tarefas de um evento
  Future<List<TaskModel>> getEventTasks(String eventId) async {
    try {
      final query = await _firestore
          .collection(_tasksCollection)
          .where('eventId', isEqualTo: eventId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs.map((doc) => TaskModel.fromMap(doc.data())).toList();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar tarefas: ${e.toString()}');
    }
  }

  /// Stream de tarefas de um evento
  Stream<List<TaskModel>> getEventTasksStream(String eventId) {
    return _firestore
        .collection(_tasksCollection)
        .where('eventId', isEqualTo: eventId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => TaskModel.fromMap(doc.data()))
              .toList(),
        );
  }

  /// Deleta uma tarefa
  Future<void> deleteTask(String taskId) async {
    try {
      final batch = _firestore.batch();

      // Deletar tarefa
      batch.delete(_firestore.collection(_tasksCollection).doc(taskId));

      // Deletar microtarefas relacionadas
      final microtasks = await _firestore
          .collection(_microtasksCollection)
          .where('taskId', isEqualTo: taskId)
          .get();

      for (final microtask in microtasks.docs) {
        batch.delete(microtask.reference);
      }

      await batch.commit();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao deletar tarefa: ${e.toString()}');
    }
  }

  /// CRUD para Microtarefas

  /// Cria uma microtarefa
  Future<void> createMicrotask(MicrotaskModel microtask) async {
    try {
      await _firestore
          .collection(_microtasksCollection)
          .doc(microtask.id)
          .set(microtask.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao criar microtarefa: ${e.toString()}');
    }
  }

  /// Atualiza uma microtarefa
  Future<void> updateMicrotask(MicrotaskModel microtask) async {
    try {
      await _firestore
          .collection(_microtasksCollection)
          .doc(microtask.id)
          .update(microtask.toMap());
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao atualizar microtarefa: ${e.toString()}');
    }
  }

  /// Obtém microtarefas de uma tarefa
  Future<List<MicrotaskModel>> getTaskMicrotasks(String taskId) async {
    try {
      final query = await _firestore
          .collection(_microtasksCollection)
          .where('taskId', isEqualTo: taskId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => MicrotaskModel.fromMap(doc.data()))
          .toList();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao buscar microtarefas: ${e.toString()}');
    }
  }

  /// Obtém microtarefas de um evento
  Future<List<MicrotaskModel>> getEventMicrotasks(String eventId) async {
    try {
      final query = await _firestore
          .collection(_microtasksCollection)
          .where('eventId', isEqualTo: eventId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => MicrotaskModel.fromMap(doc.data()))
          .toList();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException(
        'Erro ao buscar microtarefas do evento: ${e.toString()}',
      );
    }
  }

  /// Obtém microtarefas atribuídas a um usuário
  Future<List<MicrotaskModel>> getUserMicrotasks(String userId) async {
    try {
      final query = await _firestore
          .collection(_microtasksCollection)
          .where('assignedTo', isEqualTo: userId)
          .orderBy('assignedAt', descending: true)
          .get();

      return query.docs
          .map((doc) => MicrotaskModel.fromMap(doc.data()))
          .toList();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException(
        'Erro ao buscar microtarefas do usuário: ${e.toString()}',
      );
    }
  }

  /// Stream de microtarefas de um usuário
  Stream<List<MicrotaskModel>> getUserMicrotasksStream(String userId) {
    return _firestore
        .collection(_microtasksCollection)
        .where('assignedTo', isEqualTo: userId)
        .orderBy('assignedAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => MicrotaskModel.fromMap(doc.data()))
              .toList(),
        );
  }

  /// Obtém microtarefas disponíveis para atribuição em um evento
  Future<List<MicrotaskModel>> getAvailableMicrotasks(String eventId) async {
    try {
      final query = await _firestore
          .collection(_microtasksCollection)
          .where('eventId', isEqualTo: eventId)
          .where('status', isEqualTo: 'pending')
          .where('assignedTo', isNull: true)
          .orderBy('priority', descending: true)
          .orderBy('createdAt')
          .get();

      return query.docs
          .map((doc) => MicrotaskModel.fromMap(doc.data()))
          .toList();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException(
        'Erro ao buscar microtarefas disponíveis: ${e.toString()}',
      );
    }
  }

  /// Deleta uma microtarefa
  Future<void> deleteMicrotask(String microtaskId) async {
    try {
      await _firestore
          .collection(_microtasksCollection)
          .doc(microtaskId)
          .delete();
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao deletar microtarefa: ${e.toString()}');
    }
  }

  /// Verifica se uma TAG de evento já existe
  Future<bool> eventTagExists(String eventTag) async {
    try {
      final query = await _firestore
          .collection(_eventsCollection)
          .where('eventTag', isEqualTo: eventTag.toUpperCase())
          .limit(1)
          .get();

      return query.docs.isNotEmpty;
    } on FirebaseException catch (e) {
      throw DataException(_getFirestoreErrorMessage(e.code));
    } catch (e) {
      throw DataException('Erro ao verificar TAG: ${e.toString()}');
    }
  }

  /// Converte códigos de erro do Firestore em mensagens amigáveis
  String _getFirestoreErrorMessage(String code) {
    switch (code) {
      case 'permission-denied':
        return 'Permissão negada';
      case 'not-found':
        return 'Documento não encontrado';
      case 'already-exists':
        return 'Documento já existe';
      case 'resource-exhausted':
        return 'Cota excedida';
      case 'failed-precondition':
        return 'Condição prévia falhou';
      case 'aborted':
        return 'Operação abortada';
      case 'out-of-range':
        return 'Valor fora do intervalo';
      case 'unimplemented':
        return 'Operação não implementada';
      case 'internal':
        return 'Erro interno';
      case 'unavailable':
        return 'Serviço indisponível';
      case 'data-loss':
        return 'Perda de dados';
      case 'unauthenticated':
        return 'Não autenticado';
      case 'deadline-exceeded':
        return 'Tempo limite excedido';
      case 'cancelled':
        return 'Operação cancelada';
      default:
        return 'Erro do Firestore: $code';
    }
  }
}
