import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/utils/extensions.dart';
import '../../providers/auth_provider.dart';

/// Tela principal do aplicativo
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Constrói a AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(AppStrings.home),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: 0,
      actions: [_buildProfileButton()],
    );
  }

  /// Constrói o botão de perfil
  Widget _buildProfileButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;

        return PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          icon: CircleAvatar(
            radius: 16,
            backgroundImage: user?.photoUrl != null
                ? NetworkImage(user!.photoUrl!)
                : null,
            backgroundColor: AppColors.primaryContainer,
            child: user?.photoUrl == null
                ? Text(
                    user?.name.substring(0, 1).toUpperCase() ?? 'U',
                    style: const TextStyle(
                      color: AppColors.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  const Icon(Icons.person),
                  const SizedBox(width: 8),
                  Text(AppStrings.profile),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  const Icon(Icons.settings),
                  const SizedBox(width: 8),
                  Text(AppStrings.settings),
                ],
              ),
            ),
            const PopupMenuDivider(),
            PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  const Icon(Icons.logout, color: AppColors.error),
                  const SizedBox(width: 8),
                  Text(
                    AppStrings.logout,
                    style: const TextStyle(color: AppColors.error),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// Constrói o corpo da tela
  Widget _buildBody() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Saudação
              _buildGreeting(user?.name ?? 'Usuário'),

              const SizedBox(height: 24),

              // Ações rápidas
              _buildQuickActions(),

              const SizedBox(height: 24),

              // Lista de eventos (placeholder)
              _buildEventsSection(),
            ],
          ),
        );
      },
    );
  }

  /// Constrói a saudação
  Widget _buildGreeting(String userName) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${AppStrings.welcome}, $userName!',
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onBackground,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Gerencie seus eventos e tarefas',
          style: context.textTheme.bodyLarge?.copyWith(
            color: AppColors.onBackground.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Constrói as ações rápidas
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ações Rápidas',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.onBackground,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: AppStrings.createEvent,
                icon: Icons.add_circle,
                color: AppColors.primary,
                onTap: _handleCreateEvent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                title: AppStrings.joinEvent,
                icon: Icons.group_add,
                color: AppColors.secondary,
                onTap: _handleJoinEvent,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Constrói um card de ação
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói a seção de eventos
  Widget _buildEventsSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.myEvents,
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.onBackground,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(child: _buildEventsPlaceholder()),
        ],
      ),
    );
  }

  /// Constrói o placeholder para eventos
  Widget _buildEventsPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note,
            size: 64,
            color: AppColors.onBackground.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            AppStrings.noEventsFound,
            style: context.textTheme.bodyLarge?.copyWith(
              color: AppColors.onBackground.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Crie um evento ou entre em um existente',
            style: context.textTheme.bodyMedium?.copyWith(
              color: AppColors.onBackground.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói o botão flutuante
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _handleCreateEvent,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      child: const Icon(Icons.add),
    );
  }

  /// Manipula seleção do menu
  void _handleMenuSelection(String value) {
    switch (value) {
      case 'profile':
        _navigateToProfile();
        break;
      case 'settings':
        _navigateToSettings();
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  /// Navega para o perfil
  void _navigateToProfile() {
    // TODO: Implementar navegação para perfil
    context.showSnackBar('Perfil em desenvolvimento');
  }

  /// Navega para configurações
  void _navigateToSettings() {
    // TODO: Implementar navegação para configurações
    context.showSnackBar('Configurações em desenvolvimento');
  }

  /// Manipula logout
  void _handleLogout() async {
    final confirmed = await context.showConfirmDialog(
      title: 'Confirmar Logout',
      content: AppStrings.confirmLogout,
    );

    if (confirmed == true && mounted) {
      final authProvider = context.read<AuthProvider>();
      await authProvider.signOut();
    }
  }

  /// Manipula criação de evento
  void _handleCreateEvent() {
    // TODO: Implementar navegação para criar evento
    context.showSnackBar('Criar evento em desenvolvimento');
  }

  /// Manipula entrada em evento
  void _handleJoinEvent() {
    // TODO: Implementar navegação para entrar em evento
    context.showSnackBar('Entrar em evento em desenvolvimento');
  }
}
