import 'package:flutter/material.dart';
import 'helpers.dart';

/// Extensões para DateTime
extension DateTimeExtensions on DateTime {
  /// Formata para exibição (dd/MM/yyyy)
  String get formatted => Helpers.formatDate(this);

  /// Formata com hora (dd/MM/yyyy HH:mm)
  String get formattedWithTime => Helpers.formatDateTime(this);

  /// Formata apenas a hora (HH:mm)
  String get timeFormatted => Helpers.formatTime(this);

  /// Verifica se é hoje
  bool get isToday => Helpers.isToday(this);

  /// Verifica se está no passado
  bool get isInPast => Helpers.isInPast(this);

  /// Calcula dias até esta data
  int daysUntil(DateTime other) => Helpers.daysBetween(this, other);
}

/// Extensões para String
extension StringExtensions on String {
  /// Capitaliza primeira letra
  String get capitalized => Helpers.capitalize(this);

  /// Remove acentos
  String get withoutAccents => Helpers.removeAccents(this);

  /// Normaliza para busca
  String get normalized => Helpers.normalizeForSearch(this);

  /// Trunca com reticências
  String truncate(int maxLength) => Helpers.truncateText(this, maxLength);

  /// Verifica se é email válido
  bool get isValidEmail => Helpers.isValidEmail(this);

  /// Converte em lista separada por vírgulas
  List<String> get asList => Helpers.splitString(this);

  /// Gera cor baseada na string
  Color get asColor => Color(Helpers.generateColorFromString(this));

  /// Verifica se string não é nula nem vazia
  bool get isNotNullOrEmpty => isNotEmpty;

  /// Verifica se string é nula ou vazia
  bool get isNullOrEmpty => isEmpty;
}

/// Extensões para List<String>
extension StringListExtensions on List<String> {
  /// Converte em string separada por vírgulas
  String get joined => Helpers.joinList(this);

  /// Remove itens vazios
  List<String> get withoutEmpty =>
      where((item) => item.trim().isNotEmpty).toList();

  /// Normaliza todos os itens para busca
  List<String> get normalized => map((item) => item.normalized).toList();
}

/// Extensões para int (duração em minutos)
extension DurationExtensions on int {
  /// Formata duração em minutos para texto legível
  String get asDuration => Helpers.formatDuration(this);

  /// Converte minutos para Duration
  Duration get minutes => Duration(minutes: this);

  /// Converte horas para Duration
  Duration get hours => Duration(hours: this);

  /// Converte dias para Duration
  Duration get days => Duration(days: this);
}

/// Extensões para double (porcentagem)
extension PercentageExtensions on double {
  /// Formata como porcentagem
  String get asPercentage => Helpers.formatPercentage(this);

  /// Limita entre 0 e 100
  double get clampedPercentage => clamp(0.0, 100.0).toDouble();
}

/// Extensões para BuildContext
extension BuildContextExtensions on BuildContext {
  /// Acesso rápido ao Theme
  ThemeData get theme => Theme.of(this);

  /// Acesso rápido ao ColorScheme
  ColorScheme get colorScheme => theme.colorScheme;

  /// Acesso rápido ao TextTheme
  TextTheme get textTheme => theme.textTheme;

  /// Acesso rápido ao MediaQuery
  MediaQueryData get mediaQuery => MediaQuery.of(this);

  /// Acesso rápido ao tamanho da tela
  Size get screenSize => mediaQuery.size;

  /// Verifica se é tela pequena (mobile)
  bool get isSmallScreen => screenSize.width < 600;

  /// Verifica se é tela média (tablet)
  bool get isMediumScreen => screenSize.width >= 600 && screenSize.width < 1200;

  /// Verifica se é tela grande (desktop)
  bool get isLargeScreen => screenSize.width >= 1200;

  /// Mostra SnackBar
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? colorScheme.error : null,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Mostra dialog de confirmação
  Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = 'Confirmar',
    String cancelText = 'Cancelar',
  }) {
    return showDialog<bool>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// Fecha teclado
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
}

/// Extensões para TimeOfDay
extension TimeOfDayExtensions on TimeOfDay {
  /// Converte para string no formato HH:mm
  String get formatted =>
      '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

  /// Converte para DateTime (usando data atual)
  DateTime get asDateTime {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  /// Converte para minutos desde meia-noite
  int get totalMinutes => hour * 60 + minute;
}

/// Extensões para Color
extension ColorExtensions on Color {
  /// Retorna cor mais clara
  Color get lighter => Color.lerp(this, Colors.white, 0.3) ?? this;

  /// Retorna cor mais escura
  Color get darker => Color.lerp(this, Colors.black, 0.3) ?? this;

  /// Converte para string hexadecimal
  String get hex =>
      '#${toARGB32().toRadixString(16).padLeft(8, '0').substring(2)}';
}
